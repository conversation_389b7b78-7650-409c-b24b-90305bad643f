#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import os
import json

def analyze_folder_content(folder_path):
    """
    分析文件夹内容，判断主要文件类型
    返回: 'image' 如果大多数是图片, 'other' 其他情况
    """
    if not os.path.exists(folder_path):
        return 'other'
    
    files = os.listdir(folder_path)
    if not files:
        return 'other'
    
    image_count = 0
    other_count = 0
    image_extensions = {'.png', '.jpg', '.jpeg', '.gif', '.bmp', '.tiff', '.svg'}
    
    for file in files:
        if os.path.isfile(os.path.join(folder_path, file)):
            file_ext = os.path.splitext(file)[1].lower()
            if file_ext in image_extensions:
                image_count += 1
            else:
                other_count += 1
    
    total_files = image_count + other_count
    if total_files == 0:
        return 'other'
    
    # 如果图片占大多数，返回'image'
    if image_count > other_count:
        return 'image'
    else:
        return 'other'

def extract_file_paths(root_dir):
    """
    按目录结构提取所有文件路径，根据文件夹内容决定记录方式
    """
    result = {}
    
    for root, dirs, files in os.walk(root_dir):
        # 获取相对路径
        rel_root = os.path.relpath(root, root_dir)
        if rel_root == '.':
            continue  # 跳过根目录
        
        # 分析当前文件夹内容
        content_type = analyze_folder_content(root)
        
        if content_type == 'image':
            # 大多数是图片，只记录文件夹路径
            result[rel_root] = "图片文件夹"
        else:
            # 大多数是其他类型文件，记录具体文件
            if files:
                file_paths = []
                for file in files:
                    file_rel_path = os.path.join(rel_root, file)
                    file_paths.append(file_rel_path)
                
                if file_paths:
                    result[rel_root] = file_paths
    
    return result

def main():
    # 获取当前脚本所在目录作为根目录
    root_directory = os.path.dirname(os.path.abspath(__file__))
    
    file_paths = extract_file_paths(root_directory)
    
    # 输出JSON格式结果
    json_output = json.dumps(file_paths, ensure_ascii=False, indent=2)
    
    # 保存到文件
    output_file = os.path.join(root_directory, "file_paths.json")
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(json_output)
    
    print(json_output)
    return file_paths

if __name__ == "__main__":
    main()