#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件匹配工具 - GUI版本
融合 extract_files.py、extract_excel_names.py、match_files.py
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import threading
import os
import sys
import json
import pandas as pd
import re
from pathlib import Path

class FileMatcherGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("文件匹配工具 - 一键匹配系统")
        self.root.geometry("800x700")
        self.root.resizable(True, True)
        
        # 变量
        self.excel_path = tk.StringVar()
        self.folder_path = tk.StringVar()
        self.output_path = tk.StringVar(value=os.getcwd())
        
        # 创建界面
        self.create_widgets()
        
        # 数据存储
        self.excel_names = {}
        self.file_paths = {}
        self.results = []
        
    def create_widgets(self):
        """创建GUI组件"""
        # 主标题
        title_frame = ttk.Frame(self.root)
        title_frame.pack(fill='x', padx=10, pady=5)
        
        title_label = ttk.Label(title_frame, text="🚀 文件匹配工具", font=('Arial', 16, 'bold'))
        title_label.pack()
        
        subtitle_label = ttk.Label(title_frame, text="一键提取Excel名称、扫描文件夹、智能匹配", font=('Arial', 10))
        subtitle_label.pack()
        
        # 分隔线
        ttk.Separator(self.root, orient='horizontal').pack(fill='x', padx=10, pady=5)
        
        # 文件选择区域
        self.create_file_selection_area()
        
        # 控制按钮区域
        self.create_control_area()
        
        # 进度显示区域
        self.create_progress_area()
        
        # 日志输出区域
        self.create_log_area()
        
        # 状态栏
        self.create_status_bar()
    
    def create_file_selection_area(self):
        """创建文件选择区域"""
        selection_frame = ttk.LabelFrame(self.root, text="📁 路径选择", padding=10)
        selection_frame.pack(fill='x', padx=10, pady=5)
        
        # Excel文件选择
        excel_frame = ttk.Frame(selection_frame)
        excel_frame.pack(fill='x', pady=2)
        
        ttk.Label(excel_frame, text="Excel表格:", width=12).pack(side='left')
        ttk.Entry(excel_frame, textvariable=self.excel_path, width=50).pack(side='left', padx=5, fill='x', expand=True)
        ttk.Button(excel_frame, text="浏览", command=self.select_excel_file, width=8).pack(side='right')
        
        # 文件夹选择
        folder_frame = ttk.Frame(selection_frame)
        folder_frame.pack(fill='x', pady=2)
        
        ttk.Label(folder_frame, text="目标文件夹:", width=12).pack(side='left')
        ttk.Entry(folder_frame, textvariable=self.folder_path, width=50).pack(side='left', padx=5, fill='x', expand=True)
        ttk.Button(folder_frame, text="浏览", command=self.select_folder, width=8).pack(side='right')
        
        # 输出路径选择
        output_frame = ttk.Frame(selection_frame)
        output_frame.pack(fill='x', pady=2)
        
        ttk.Label(output_frame, text="输出目录:", width=12).pack(side='left')
        ttk.Entry(output_frame, textvariable=self.output_path, width=50).pack(side='left', padx=5, fill='x', expand=True)
        ttk.Button(output_frame, text="浏览", command=self.select_output_folder, width=8).pack(side='right')
    
    def create_control_area(self):
        """创建控制按钮区域"""
        control_frame = ttk.LabelFrame(self.root, text="🎯 操作控制", padding=10)
        control_frame.pack(fill='x', padx=10, pady=5)
        
        button_frame = ttk.Frame(control_frame)
        button_frame.pack()
        
        # 主要按钮
        self.start_button = ttk.Button(button_frame, text="🚀 开始匹配", command=self.start_matching, style='Accent.TButton')
        self.start_button.pack(side='left', padx=5)
        
        self.clear_button = ttk.Button(button_frame, text="🗑️ 清空日志", command=self.clear_log)
        self.clear_button.pack(side='left', padx=5)
        
        self.open_button = ttk.Button(button_frame, text="📂 打开输出目录", command=self.open_output_folder)
        self.open_button.pack(side='left', padx=5)
        
        # 选项
        options_frame = ttk.Frame(control_frame)
        options_frame.pack(fill='x', pady=5)
        
        self.auto_open_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(options_frame, text="完成后自动打开结果文件", variable=self.auto_open_var).pack(side='left')
    
    def create_progress_area(self):
        """创建进度显示区域"""
        progress_frame = ttk.LabelFrame(self.root, text="📊 进度状态", padding=10)
        progress_frame.pack(fill='x', padx=10, pady=5)
        
        # 当前任务标签
        self.current_task_label = ttk.Label(progress_frame, text="等待开始...", font=('Arial', 10))
        self.current_task_label.pack(anchor='w')
        
        # 进度条
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(progress_frame, variable=self.progress_var, maximum=100, length=400)
        self.progress_bar.pack(fill='x', pady=5)
        
        # 统计信息
        stats_frame = ttk.Frame(progress_frame)
        stats_frame.pack(fill='x')
        
        self.stats_label = ttk.Label(stats_frame, text="", font=('Arial', 9))
        self.stats_label.pack(anchor='w')
    
    def create_log_area(self):
        """创建日志输出区域"""
        log_frame = ttk.LabelFrame(self.root, text="📝 运行日志", padding=5)
        log_frame.pack(fill='both', expand=True, padx=10, pady=5)
        
        # 日志文本框
        self.log_text = scrolledtext.ScrolledText(log_frame, height=12, wrap=tk.WORD, font=('Consolas', 9))
        self.log_text.pack(fill='both', expand=True)
        
        # 配置颜色标签
        self.log_text.tag_config("info", foreground="black")
        self.log_text.tag_config("success", foreground="green")
        self.log_text.tag_config("warning", foreground="orange")
        self.log_text.tag_config("error", foreground="red")
        self.log_text.tag_config("progress", foreground="blue")
    
    def create_status_bar(self):
        """创建状态栏"""
        self.status_bar = ttk.Label(self.root, text="就绪", relief=tk.SUNKEN, anchor=tk.W)
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X)
    
    def select_excel_file(self):
        """选择Excel文件"""
        file_path = filedialog.askopenfilename(
            title="选择Excel文件",
            filetypes=[("Excel文件", "*.xlsx *.xls"), ("所有文件", "*.*")]
        )
        if file_path:
            self.excel_path.set(file_path)
            self.log_message(f"已选择Excel文件: {file_path}", "info")
    
    def select_folder(self):
        """选择目标文件夹"""
        folder_path = filedialog.askdirectory(title="选择目标文件夹")
        if folder_path:
            self.folder_path.set(folder_path)
            self.log_message(f"已选择目标文件夹: {folder_path}", "info")
    
    def select_output_folder(self):
        """选择输出目录"""
        folder_path = filedialog.askdirectory(title="选择输出目录")
        if folder_path:
            self.output_path.set(folder_path)
            self.log_message(f"已选择输出目录: {folder_path}", "info")
    
    def log_message(self, message, level="info"):
        """添加日志消息"""
        self.log_text.insert(tk.END, f"{message}\n", level)
        self.log_text.see(tk.END)
        self.root.update_idletasks()
    
    def clear_log(self):
        """清空日志"""
        self.log_text.delete(1.0, tk.END)
    
    def open_output_folder(self):
        """打开输出目录"""
        output_dir = self.output_path.get()
        if os.path.exists(output_dir):
            if sys.platform == "win32":
                os.startfile(output_dir)
            elif sys.platform == "darwin":
                os.system(f"open '{output_dir}'")
            else:
                os.system(f"xdg-open '{output_dir}'")
    
    def update_progress(self, value, message=""):
        """更新进度"""
        self.progress_var.set(value)
        if message:
            self.current_task_label.config(text=message)
        self.root.update_idletasks()
    
    def update_status(self, message):
        """更新状态栏"""
        self.status_bar.config(text=message)
    
    def validate_inputs(self):
        """验证输入"""
        if not self.excel_path.get():
            messagebox.showerror("错误", "请选择Excel文件！")
            return False
        
        if not os.path.exists(self.excel_path.get()):
            messagebox.showerror("错误", "Excel文件不存在！")
            return False
        
        if not self.folder_path.get():
            messagebox.showerror("错误", "请选择目标文件夹！")
            return False
        
        if not os.path.exists(self.folder_path.get()):
            messagebox.showerror("错误", "目标文件夹不存在！")
            return False
        
        if not self.output_path.get():
            messagebox.showerror("错误", "请选择输出目录！")
            return False
        
        return True
    
    def start_matching(self):
        """开始匹配过程"""
        if not self.validate_inputs():
            return
        
        # 禁用开始按钮
        self.start_button.config(state='disabled')
        self.update_status("正在处理...")
        
        # 在新线程中运行匹配过程
        thread = threading.Thread(target=self.run_matching_process)
        thread.daemon = True
        thread.start()
    
    def run_matching_process(self):
        """运行匹配过程（在后台线程中）"""
        try:
            # 步骤1：提取Excel名称
            self.update_progress(10, "正在提取Excel表格中的名称...")
            self.log_message("=" * 60, "info")
            self.log_message("🚀 开始文件匹配过程", "success")
            self.log_message("=" * 60, "info")
            
            self.excel_names = self.extract_excel_names()
            if not self.excel_names:
                self.log_message("❌ Excel名称提取失败", "error")
                return
            
            # 步骤2：扫描文件夹
            self.update_progress(30, "正在扫描目标文件夹...")
            self.file_paths = self.extract_file_paths()
            if not self.file_paths:
                self.log_message("❌ 文件夹扫描失败", "error")
                return
            
            # 步骤3：执行匹配
            self.update_progress(50, "正在执行智能匹配...")
            self.results = self.find_matches()
            
            # 步骤4：生成结果
            self.update_progress(80, "正在生成结果文件...")
            success = self.generate_results()
            
            if success:
                self.update_progress(100, "✅ 匹配完成！")
                self.log_message("🎉 文件匹配任务完成！", "success")
                self.log_message("=" * 60, "info")
                
                # 自动打开结果
                if self.auto_open_var.get():
                    self.open_result_file()
            else:
                self.log_message("❌ 结果生成失败", "error")
                
        except Exception as e:
            self.log_message(f"❌ 处理过程中发生错误: {str(e)}", "error")
            messagebox.showerror("错误", f"处理失败:\n{str(e)}")
        finally:
            # 重新启用开始按钮
            self.start_button.config(state='normal')
            self.update_status("就绪")
    
    def extract_excel_names(self):
        """提取Excel名称（基于extract_excel_names.py）"""
        try:
            excel_file = self.excel_path.get()
            self.log_message(f"📊 正在处理Excel文件: {os.path.basename(excel_file)}", "info")
            
            excel_data = pd.ExcelFile(excel_file)
            result = {}
            total_names_count = 0
            
            for sheet_name in excel_data.sheet_names:
                self.log_message(f"   处理工作表: {sheet_name}", "info")
                
                try:
                    df = pd.read_excel(excel_file, sheet_name=sheet_name, header=None)
                    first_column_data = []
                    names_count = 0
                    
                    for index, row in df.iterrows():
                        if len(row) > 0:
                            cell_value = row[0]
                            
                            if pd.isna(cell_value) or str(cell_value).strip() == '':
                                has_content_after = False
                                for col_idx in range(1, len(row)):
                                    if not pd.isna(row[col_idx]) and str(row[col_idx]).strip() != '':
                                        has_content_after = True
                                        break
                                if has_content_after:
                                    first_column_data.append("")
                            else:
                                cell_str = str(cell_value).strip()
                                if cell_str != "名称":
                                    first_column_data.append(cell_str)
                                    names_count += 1
                    
                    result[f"{sheet_name}({names_count})"] = first_column_data
                    total_names_count += names_count
                    
                except Exception as e:
                    self.log_message(f"   ⚠️ 工作表 {sheet_name} 处理失败: {str(e)}", "warning")
                    result[f"{sheet_name}(0)"] = []
            
            self.log_message(f"✅ Excel处理完成，共提取 {total_names_count} 个名称", "success")
            
            # 保存中间结果
            excel_names_file = os.path.join(self.output_path.get(), "excel_names.json")
            with open(excel_names_file, 'w', encoding='utf-8') as f:
                json.dump(result, f, ensure_ascii=False, indent=2)
            
            return result
            
        except Exception as e:
            self.log_message(f"❌ Excel处理失败: {str(e)}", "error")
            return {}
    
    def extract_file_paths(self):
        """提取文件路径（基于extract_files.py）"""
        try:
            root_dir = self.folder_path.get()
            self.log_message(f"📁 正在扫描文件夹: {root_dir}", "info")
            
            result = {}
            image_extensions = {'.png', '.jpg', '.jpeg', '.gif', '.bmp', '.tiff', '.svg'}
            
            file_count = 0
            folder_count = 0
            
            for root, dirs, files in os.walk(root_dir):
                rel_root = os.path.relpath(root, root_dir)
                if rel_root == '.':
                    continue
                
                # 分析文件夹内容
                if files:
                    image_count = 0
                    other_count = 0
                    
                    for file in files:
                        if os.path.isfile(os.path.join(root, file)):
                            file_ext = os.path.splitext(file)[1].lower()
                            if file_ext in image_extensions:
                                image_count += 1
                            else:
                                other_count += 1
                    
                    if image_count > other_count:
                        # 大多数是图片，只记录文件夹
                        result[rel_root] = "图片文件夹"
                        folder_count += 1
                    else:
                        # 记录具体文件
                        file_paths = []
                        for file in files:
                            file_rel_path = os.path.join(rel_root, file)
                            file_paths.append(file_rel_path)
                        result[rel_root] = file_paths
                        file_count += len(file_paths)
            
            self.log_message(f"✅ 文件夹扫描完成，发现 {file_count} 个文件和 {folder_count} 个图片文件夹", "success")
            
            # 保存中间结果
            file_paths_file = os.path.join(self.output_path.get(), "file_paths.json")
            with open(file_paths_file, 'w', encoding='utf-8') as f:
                json.dump(result, f, ensure_ascii=False, indent=2)
            
            return result
            
        except Exception as e:
            self.log_message(f"❌ 文件夹扫描失败: {str(e)}", "error")
            return {}
    
    def find_matches(self):
        """执行匹配（基于match_files.py的核心算法）"""
        try:
            self.log_message("🎯 开始智能匹配算法...", "info")
            
            # 合并所有文件路径
            all_files = []
            for category, files in self.file_paths.items():
                if isinstance(files, list):
                    all_files.extend(files)
                elif isinstance(files, str) and files == "图片文件夹":
                    all_files.append(category)
            
            self.log_message(f"   已加载 {len(all_files)} 个文件/文件夹路径", "info")
            
            # 收集Excel名称
            excel_items = []
            for sheet_info, names in self.excel_names.items():
                if isinstance(names, list):
                    sheet_name = sheet_info.split('(')[0] if '(' in sheet_info else sheet_info
                    for name in names:
                        excel_items.append({
                            'sheet': sheet_name,
                            'name': name,
                            'original_order': len(excel_items)
                        })
            
            total_names = len(excel_items)
            self.log_message(f"   开始匹配 {total_names} 个名称", "info")
            
            # 计算匹配分数
            all_matches = []
            for i, excel_item in enumerate(excel_items):
                name = excel_item['name']
                if name:
                    for file_path in all_files:
                        if self.fuzzy_match(name, file_path):
                            score = self.calculate_match_score(name, file_path)
                            if score > 0:
                                all_matches.append({
                                    'excel_item': excel_item,
                                    'file_path': file_path,
                                    'score': score
                                })
                
                # 更新进度
                if i % 20 == 0:
                    progress = 50 + (i / total_names) * 20
                    self.update_progress(progress, f"正在匹配... ({i}/{total_names})")
            
            self.log_message(f"   找到 {len(all_matches)} 个潜在匹配", "info")
            
            # 贪心分配
            all_matches.sort(key=lambda x: x['score'], reverse=True)
            allocated_files = set()
            allocated_names = set()
            final_matches = {}
            
            for match in all_matches:
                excel_item = match['excel_item']
                file_path = match['file_path']
                name_key = (excel_item['sheet'], excel_item['name'], excel_item['original_order'])
                
                if file_path not in allocated_files and name_key not in allocated_names:
                    final_matches[name_key] = {
                        'file_path': file_path,
                        'score': match['score']
                    }
                    allocated_files.add(file_path)
                    allocated_names.add(name_key)
            
            # 构建结果
            results = []
            for excel_item in excel_items:
                name_key = (excel_item['sheet'], excel_item['name'], excel_item['original_order'])
                
                if excel_item['name']:
                    if name_key in final_matches:
                        match_info = final_matches[name_key]
                        file_path = match_info['file_path']
                        match_type = "文件夹" if file_path in self.file_paths and self.file_paths[file_path] == "图片文件夹" else "文件"
                        
                        results.append({
                            "序号": len(results) + 1,
                            "工作表": excel_item['sheet'],
                            "名称": excel_item['name'],
                            "匹配路径": file_path,
                            "匹配类型": match_type,
                            "匹配分数": match_info['score'],
                            "匹配状态": "成功"
                        })
                    else:
                        results.append({
                            "序号": len(results) + 1,
                            "工作表": excel_item['sheet'],
                            "名称": excel_item['name'],
                            "匹配路径": "",
                            "匹配类型": "",
                            "匹配分数": 0,
                            "匹配状态": "失败"
                        })
                else:
                    results.append({
                        "序号": len(results) + 1,
                        "工作表": excel_item['sheet'],
                        "名称": "",
                        "匹配路径": "",
                        "匹配类型": "",
                        "匹配分数": 0,
                        "匹配状态": "空名称"
                    })
            
            # 统计结果
            successful = len([r for r in results if r["匹配状态"] == "成功"])
            failed = len([r for r in results if r["匹配状态"] == "失败"])
            empty = len([r for r in results if r["匹配状态"] == "空名称"])
            success_rate = successful / len(results) * 100 if results else 0
            
            self.log_message(f"✅ 匹配完成！成功 {successful} 个，失败 {failed} 个，空名称 {empty} 个", "success")
            self.log_message(f"   成功率: {success_rate:.1f}%", "success")
            
            # 更新统计显示
            stats_text = f"总计: {len(results)} | 成功: {successful} | 失败: {failed} | 成功率: {success_rate:.1f}%"
            self.stats_label.config(text=stats_text)
            
            return results
            
        except Exception as e:
            self.log_message(f"❌ 匹配过程失败: {str(e)}", "error")
            return []
    
    def fuzzy_match(self, excel_name, file_path):
        """模糊匹配函数"""
        if not excel_name or not file_path:
            return False
        
        filename = os.path.splitext(os.path.basename(file_path))[0]
        excel_name_clean = excel_name.strip()
        
        # 精确匹配
        if excel_name_clean.lower() == filename.lower():
            return True
        
        # 路径匹配
        path_parts = file_path.split(os.sep)
        for part in path_parts:
            if excel_name_clean.lower() == part.lower():
                return True
        
        # 包含匹配
        if excel_name_clean.lower() in filename.lower():
            return True
        
        if excel_name_clean.lower() in file_path.lower():
            return True
        
        if filename.lower() in excel_name_clean.lower():
            return True
        
        # 去除特殊字符后匹配
        excel_clean = re.sub(r'[^\w\s]', '', excel_name_clean).lower()
        file_clean = re.sub(r'[^\w\s]', '', filename).lower()
        
        if excel_clean and file_clean and (excel_clean in file_clean or file_clean in excel_clean):
            return True
        
        return False
    
    def calculate_match_score(self, excel_name, file_path):
        """计算匹配分数"""
        if not excel_name or not file_path:
            return 0
        
        filename = os.path.splitext(os.path.basename(file_path))[0]
        excel_name_clean = excel_name.strip().lower()
        filename_lower = filename.lower()
        
        path_parts = file_path.split(os.sep)
        last_folder = path_parts[-1].lower() if path_parts else ""
        
        # 关键词权重
        unique_keywords = ["企业参观", "访问", "任务操作中心", "深空网络", "地面站", "发射场", "组装厂"]
        common_keywords = ["技术", "系统", "网络", "中心", "卫星", "火箭"]
        
        unique_matches = sum(1 for keyword in unique_keywords if keyword.lower() in excel_name_clean)
        common_matches = sum(1 for keyword in common_keywords if keyword.lower() in excel_name_clean)
        
        # 基础分数
        base_score = 0
        
        if excel_name_clean == last_folder:
            base_score = 100
        elif excel_name_clean == filename_lower:
            base_score = 95
        elif any(excel_name_clean == part.lower() for part in path_parts):
            base_score = 90
        elif excel_name_clean in last_folder:
            base_score = 85
        elif excel_name_clean in filename_lower:
            base_score = 80
        elif filename_lower in excel_name_clean:
            base_score = 70
        elif excel_name_clean in file_path.lower():
            base_score = 60
        else:
            excel_clean = re.sub(r'[^\w\s]', '', excel_name_clean)
            file_clean = re.sub(r'[^\w\s]', '', filename_lower)
            folder_clean = re.sub(r'[^\w\s]', '', last_folder)
            
            if excel_clean and folder_clean:
                if excel_clean == folder_clean:
                    base_score = 55
                elif excel_clean in folder_clean or folder_clean in excel_clean:
                    base_score = 50
            
            if excel_clean and file_clean and base_score == 0:
                if excel_clean == file_clean:
                    base_score = 45
                elif excel_clean in file_clean or file_clean in excel_clean:
                    base_score = 40
        
        if base_score == 0:
            return 0
        
        # 加权计算
        name_length = len(excel_name_clean)
        file_length = len(filename_lower)
        
        if name_length > 0 and file_length > 0:
            length_ratio = min(name_length, file_length) / max(name_length, file_length)
            length_bonus = length_ratio * 10
        else:
            length_bonus = 0
        
        unique_bonus = unique_matches * 15
        common_bonus = common_matches * 3
        depth_bonus = 5 if len(path_parts) >= 4 else 0
        
        final_score = base_score + length_bonus + unique_bonus + common_bonus + depth_bonus
        return min(final_score, 150)
    
    def split_path_columns(self, file_path, max_levels=6):
        """将路径分解为多列"""
        if not file_path:
            return [""] * max_levels
        
        parts = file_path.split(os.sep)
        
        while len(parts) < max_levels:
            parts.append("")
        
        if len(parts) > max_levels:
            last_part = os.sep.join(parts[max_levels-1:])
            parts = parts[:max_levels-1] + [last_part]
        
        return parts[:max_levels]
    
    def generate_results(self):
        """生成结果文件"""
        try:
            self.log_message("📊 正在生成Excel结果文件...", "info")
            
            # 创建扩展的结果数据（包含路径分列）
            extended_results = []
            for result in self.results:
                path_parts = self.split_path_columns(result["匹配路径"], max_levels=6)
                
                extended_result = {
                    "序号": result["序号"],
                    "工作表": result["工作表"],
                    "名称": result["名称"],
                    "匹配路径": result["匹配路径"],
                    "路径1": path_parts[0],
                    "路径2": path_parts[1],
                    "路径3": path_parts[2],
                    "路径4": path_parts[3],
                    "路径5": path_parts[4],
                    "路径6": path_parts[5],
                    "匹配类型": result["匹配类型"],
                    "匹配分数": result.get("匹配分数", 0),
                    "匹配状态": result["匹配状态"]
                }
                extended_results.append(extended_result)
            
            df = pd.DataFrame(extended_results)
            
            # 保存到Excel（按工作表分副表）
            output_file = os.path.join(self.output_path.get(), "匹配结果.xlsx")
            
            with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
                # 汇总结果
                df.to_excel(writer, sheet_name='汇总结果', index=False)
                self.log_message("   创建汇总结果表", "info")
                
                # 按工作表分副表
                sheet_names = []
                for sheet_info in self.excel_names.keys():
                    sheet_name = sheet_info.split('(')[0] if '(' in sheet_info else sheet_info
                    if sheet_name not in sheet_names:
                        sheet_names.append(sheet_name)
                
                for sheet_name in sheet_names:
                    sheet_data = [result for result in extended_results if result["工作表"] == sheet_name]
                    
                    if sheet_data:
                        sheet_df = pd.DataFrame(sheet_data)
                        safe_sheet_name = sheet_name.replace('/', '_').replace('\\', '_').replace('*', '_').replace('[', '_').replace(']', '_').replace(':', '_').replace('?', '_')
                        if len(safe_sheet_name) > 31:
                            safe_sheet_name = safe_sheet_name[:28] + "..."
                        
                        sheet_df.to_excel(writer, sheet_name=safe_sheet_name, index=False)
                        self.log_message(f"   创建副表: {safe_sheet_name} ({len(sheet_data)} 条记录)", "info")
            
            self.log_message(f"✅ Excel文件已生成: {output_file}", "success")
            
            # 保存统计信息
            stats_file = os.path.join(self.output_path.get(), "匹配统计.json")
            stats_data = {
                "总体统计": {
                    "总计": len(self.results),
                    "成功": len([r for r in self.results if r["匹配状态"] == "成功"]),
                    "失败": len([r for r in self.results if r["匹配状态"] == "失败"]),
                    "空名称": len([r for r in self.results if r["匹配状态"] == "空名称"])
                }
            }
            
            with open(stats_file, 'w', encoding='utf-8') as f:
                json.dump(stats_data, f, ensure_ascii=False, indent=2)
            
            self.log_message(f"✅ 统计文件已生成: {stats_file}", "success")
            self.result_file_path = output_file
            
            return True
            
        except Exception as e:
            self.log_message(f"❌ 结果生成失败: {str(e)}", "error")
            return False
    
    def open_result_file(self):
        """打开结果文件"""
        try:
            if hasattr(self, 'result_file_path') and os.path.exists(self.result_file_path):
                if sys.platform == "win32":
                    os.startfile(self.result_file_path)
                elif sys.platform == "darwin":
                    os.system(f"open '{self.result_file_path}'")
                else:
                    os.system(f"xdg-open '{self.result_file_path}'")
                self.log_message("📂 已打开结果文件", "info")
        except Exception as e:
            self.log_message(f"⚠️ 无法打开结果文件: {str(e)}", "warning")

def main():
    # 检查依赖库
    try:
        import pandas as pd
        import openpyxl
    except ImportError as e:
        messagebox.showerror("依赖库缺失", f"请安装必要的库:\npip install pandas openpyxl\n\n错误: {str(e)}")
        return
    
    # 创建GUI
    root = tk.Tk()
    app = FileMatcherGUI(root)
    
    # 设置图标（如果有的话）
    try:
        # root.iconbitmap("icon.ico")  # 如果有图标文件
        pass
    except:
        pass
    
    # 运行GUI
    root.mainloop()

if __name__ == "__main__":
    main()