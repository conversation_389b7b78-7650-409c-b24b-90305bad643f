#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import pandas as pd
import json
import os
import re
import sys

def print_progress_bar(current, total, prefix='', suffix='', length=50):
    """打印进度条"""
    percent = ("{0:.1f}").format(100 * (current / float(total)))
    filled_length = int(length * current // total)
    bar = '█' * filled_length + '-' * (length - filled_length)
    print(f'\r{prefix} |{bar}| {percent}% {suffix}', end='\r')
    if current == total:
        print()  # 完成时换行

def load_json_files():
    """加载JSON文件"""
    base_dir = os.path.dirname(os.path.abspath(__file__))
    
    excel_names_file = os.path.join(base_dir, "excel_names.json")
    file_paths_file = os.path.join(base_dir, "file_paths.json")
    
    try:
        with open(excel_names_file, 'r', encoding='utf-8') as f:
            excel_names = json.load(f)
    except:
        excel_names = {}
    
    try:
        with open(file_paths_file, 'r', encoding='utf-8') as f:
            file_paths = json.load(f)
    except:
        file_paths = {}
    
    return excel_names, file_paths

def extract_filename_from_path(file_path):
    """从路径中提取文件名（不包含扩展名）"""
    filename = os.path.basename(file_path)
    return os.path.splitext(filename)[0]

def fuzzy_match(excel_name, file_path):
    """模糊匹配函数"""
    if not excel_name or not file_path:
        return False
    
    # 处理路径和文件夹名称匹配
    filename = extract_filename_from_path(file_path)
    excel_name_clean = excel_name.strip()
    
    # 1. 精确匹配（忽略大小写）
    if excel_name_clean.lower() == filename.lower():
        return True
    
    # 2. 检查是否是文件夹路径匹配
    path_parts = file_path.split('\\')
    for part in path_parts:
        if excel_name_clean.lower() == part.lower():
            return True
    
    # 3. 部分匹配：Excel名称包含在文件名或路径中
    if excel_name_clean.lower() in filename.lower():
        return True
    
    if excel_name_clean.lower() in file_path.lower():
        return True
    
    # 4. 部分匹配：文件名包含在Excel名称中
    if filename.lower() in excel_name_clean.lower():
        return True
    
    # 5. 去除特殊字符后匹配
    excel_clean = re.sub(r'[^\w\s]', '', excel_name_clean).lower()
    file_clean = re.sub(r'[^\w\s]', '', filename).lower()
    
    if excel_clean and file_clean and (excel_clean in file_clean or file_clean in excel_clean):
        return True
    
    return False

def calculate_match_score(excel_name, file_path):
    """计算匹配分数，分数越高匹配度越好"""
    if not excel_name or not file_path:
        return 0
    
    filename = extract_filename_from_path(file_path)
    excel_name_clean = excel_name.strip().lower()
    filename_lower = filename.lower()
    
    # 获取路径中的最后一个文件夹名（可能就是要匹配的文件夹名）
    path_parts = file_path.split('\\')
    last_folder = path_parts[-1].lower() if path_parts else ""
    
    # 关键词权重系统
    unique_keywords = ["企业参观", "访问", "任务操作中心", "深空网络", "地面站", "发射场", "组装厂"]
    common_keywords = ["技术", "系统", "网络", "中心", "卫星", "火箭"]
    
    # 计算独特关键词匹配数
    unique_matches = sum(1 for keyword in unique_keywords if keyword.lower() in excel_name_clean)
    common_matches = sum(1 for keyword in common_keywords if keyword.lower() in excel_name_clean)
    
    # 基础分数
    base_score = 0
    
    # 1. 精确匹配文件夹名（最高优先级）
    if excel_name_clean == last_folder:
        base_score = 100
    # 2. 精确匹配文件名
    elif excel_name_clean == filename_lower:
        base_score = 95
    # 3. 检查路径中任何部分的精确匹配
    elif any(excel_name_clean == part.lower() for part in path_parts):
        base_score = 90
    # 4. 文件夹名包含Excel名称
    elif excel_name_clean in last_folder:
        base_score = 85
    # 5. 完全包含匹配（文件名）
    elif excel_name_clean in filename_lower:
        base_score = 80
    elif filename_lower in excel_name_clean:
        base_score = 70
    # 6. 路径包含匹配
    elif excel_name_clean in file_path.lower():
        base_score = 60
    else:
        # 7. 去除特殊字符后的匹配
        excel_clean = re.sub(r'[^\w\s]', '', excel_name_clean)
        file_clean = re.sub(r'[^\w\s]', '', filename_lower)
        folder_clean = re.sub(r'[^\w\s]', '', last_folder)
        
        if excel_clean and folder_clean:
            if excel_clean == folder_clean:
                base_score = 55
            elif excel_clean in folder_clean or folder_clean in excel_clean:
                base_score = 50
        
        if excel_clean and file_clean and base_score == 0:
            if excel_clean == file_clean:
                base_score = 45
            elif excel_clean in file_clean or file_clean in excel_clean:
                base_score = 40
    
    if base_score == 0:
        return 0
    
    # 计算完整性加权
    # 名称越长，匹配到的文件名也应该越长
    name_length = len(excel_name_clean)
    file_length = len(filename_lower)
    
    # 长度适配性评分：名称长度和文件名长度的匹配度
    if name_length > 0 and file_length > 0:
        length_ratio = min(name_length, file_length) / max(name_length, file_length)
        length_bonus = length_ratio * 10  # 最多+10分
    else:
        length_bonus = 0
    
    # 独特关键词加权：包含独特关键词的匹配应该得更高分
    unique_bonus = unique_matches * 15  # 每个独特关键词+15分
    common_bonus = common_matches * 3   # 每个通用关键词+3分
    
    # 路径深度加权：更深的路径匹配可能更精确
    if len(path_parts) >= 4:  # 至少4层路径
        depth_bonus = 5
    else:
        depth_bonus = 0
    
    final_score = base_score + length_bonus + unique_bonus + common_bonus + depth_bonus
    
    # 确保分数不超过150
    return min(final_score, 150)

def find_matches_optimized(excel_names, file_paths):
    """使用全局优化的贪心分配算法查找匹配"""
    print("正在加载文件路径...")
    
    # 合并所有文件路径到一个列表
    all_files = []
    for category, files in file_paths.items():
        if isinstance(files, list):
            all_files.extend(files)
        elif isinstance(files, str) and files == "图片文件夹":
            all_files.append(category)
    
    print(f"已加载 {len(all_files)} 个文件/文件夹路径")
    
    # 收集所有Excel名称，保持顺序
    excel_items = []
    for sheet_info, names in excel_names.items():
        if isinstance(names, list):
            sheet_name = sheet_info.split('(')[0] if '(' in sheet_info else sheet_info
            for name in names:
                excel_items.append({
                    'sheet': sheet_name,
                    'name': name,
                    'original_order': len(excel_items)
                })
    
    total_names = len(excel_items)
    print(f"开始全局优化匹配 {total_names} 个名称...")
    
    # 第一阶段：计算所有可能的匹配分数
    print("📊 第一阶段：计算匹配分数...")
    all_matches = []
    processed = 0
    
    for excel_item in excel_items:
        name = excel_item['name']
        if name:  # 非空名称
            for file_path in all_files:
                if fuzzy_match(name, file_path):
                    score = calculate_match_score(name, file_path)
                    if score > 0:
                        all_matches.append({
                            'excel_item': excel_item,
                            'file_path': file_path,
                            'score': score
                        })
        
        processed += 1
        if processed % 50 == 0:  # 每50个显示一次进度
            print_progress_bar(processed, total_names, 
                             prefix='计算分数', 
                             suffix=f'({processed}/{total_names})')
    
    print_progress_bar(total_names, total_names, prefix='计算分数', suffix='完成')
    print(f"\n找到 {len(all_matches)} 个潜在匹配")
    
    # 第二阶段：贪心分配算法
    print("🎯 第二阶段：全局优化分配...")
    
    # 按分数降序排序
    all_matches.sort(key=lambda x: x['score'], reverse=True)
    
    # 贪心分配
    allocated_files = set()
    allocated_names = set()
    final_matches = {}
    
    for match in all_matches:
        excel_item = match['excel_item']
        file_path = match['file_path']
        name_key = (excel_item['sheet'], excel_item['name'], excel_item['original_order'])
        
        # 如果文件和名称都还没有被分配
        if file_path not in allocated_files and name_key not in allocated_names:
            final_matches[name_key] = {
                'file_path': file_path,
                'score': match['score']
            }
            allocated_files.add(file_path)
            allocated_names.add(name_key)
    
    print(f"✅ 成功分配 {len(final_matches)} 个最优匹配")
    
    # 第三阶段：构建结果
    print("📋 第三阶段：构建最终结果...")
    results = []
    
    for excel_item in excel_items:
        name_key = (excel_item['sheet'], excel_item['name'], excel_item['original_order'])
        
        if excel_item['name']:  # 非空名称
            if name_key in final_matches:
                match_info = final_matches[name_key]
                file_path = match_info['file_path']
                
                # 检查匹配的是文件夹还是文件
                match_type = "文件夹" if file_path in file_paths and file_paths[file_path] == "图片文件夹" else "文件"
                
                results.append({
                    "序号": len(results) + 1,
                    "工作表": excel_item['sheet'],
                    "名称": excel_item['name'],
                    "匹配路径": file_path,
                    "匹配类型": match_type,
                    "匹配分数": match_info['score'],
                    "匹配状态": "成功"
                })
            else:
                results.append({
                    "序号": len(results) + 1,
                    "工作表": excel_item['sheet'],
                    "名称": excel_item['name'],
                    "匹配路径": "",
                    "匹配类型": "",
                    "匹配分数": 0,
                    "匹配状态": "失败"
                })
        else:
            # 空名称
            results.append({
                "序号": len(results) + 1,
                "工作表": excel_item['sheet'],
                "名称": "",
                "匹配路径": "",
                "匹配类型": "",
                "匹配分数": 0,
                "匹配状态": "空名称"
            })
    
    print(f"\n🎉 全局优化匹配完成！")
    print(f"   总处理: {len(results)} 个名称")
    print(f"   成功匹配: {len(final_matches)} 个")
    print(f"   重复匹配: 0 个 (已消除)")
    
    return results

# 保留原有的find_matches函数作为备用
def find_matches(excel_names, file_paths):
    """原始匹配算法（备用）"""
    return find_matches_optimized(excel_names, file_paths)

def check_duplicate_matches(results):
    """检测重复匹配的情况"""
    print("\n🔍 检测重复匹配...")
    
    # 按匹配路径分组
    path_groups = {}
    for result in results:
        if result["匹配状态"] == "成功" and result["匹配路径"]:
            path = result["匹配路径"]
            if path not in path_groups:
                path_groups[path] = []
            path_groups[path].append(result)
    
    # 找出重复匹配
    duplicates = {path: items for path, items in path_groups.items() if len(items) > 1}
    
    if duplicates:
        print(f"\n⚠️  发现 {len(duplicates)} 个文件/文件夹被多次匹配:")
        print("=" * 80)
        
        for path, items in duplicates.items():
            print(f"\n📁 路径: {path}")
            print(f"   类型: {items[0]['匹配类型']}")
            print(f"   被以下 {len(items)} 个名称匹配:")
            for item in items:
                print(f"   - 工作表: {item['工作表']} | 名称: {item['名称']}")
        
        print("=" * 80)
        return duplicates
    else:
        print("✅ 未发现重复匹配")
        return {}

def split_path_columns(file_path, max_levels=6):
    """将路径分解为多列，最多分解为max_levels列"""
    if not file_path:
        return [""] * max_levels
    
    parts = file_path.split('\\')
    
    # 补齐到max_levels列
    while len(parts) < max_levels:
        parts.append("")
    
    # 如果超过max_levels，合并多余的部分到最后一列
    if len(parts) > max_levels:
        last_part = '\\'.join(parts[max_levels-1:])
        parts = parts[:max_levels-1] + [last_part]
    
    return parts[:max_levels]

def create_statistics(results):
    """创建统计信息"""
    total = len(results)
    successful = len([r for r in results if r["匹配状态"] == "成功"])
    failed = len([r for r in results if r["匹配状态"] == "失败"])
    empty = len([r for r in results if r["匹配状态"] == "空名称"])
    
    return {
        "总计": total,
        "成功": successful,
        "失败": failed,
        "空名称": empty,
        "成功率": f"{successful/total*100:.1f}%" if total > 0 else "0%"
    }

def main():
    print("=" * 60)
    print("文件匹配工具 - 开始运行")
    print("=" * 60)
    
    # 加载数据
    print("📁 加载JSON文件...")
    excel_names, file_paths = load_json_files()
    
    if not excel_names:
        print("❌ 无法加载excel_names.json")
        return
    
    if not file_paths:
        print("❌ 无法加载file_paths.json")
        return
    
    print("✅ JSON文件加载成功")
    
    # 查找匹配
    print("\n🔍 开始匹配过程...")
    results = find_matches(excel_names, file_paths)
    
    # 检测重复匹配
    duplicates = check_duplicate_matches(results)
    
    # 创建扩展的DataFrame（包含路径分列）
    print("📊 生成结果表格（包含路径分列）...")
    
    # 为每个结果添加路径分列
    extended_results = []
    for result in results:
        path_parts = split_path_columns(result["匹配路径"], max_levels=6)
        
        extended_result = {
            "序号": result["序号"],
            "工作表": result["工作表"],
            "名称": result["名称"],
            "匹配路径": result["匹配路径"],
            "路径1": path_parts[0],
            "路径2": path_parts[1],
            "路径3": path_parts[2],
            "路径4": path_parts[3],
            "路径5": path_parts[4],
            "路径6": path_parts[5],
            "匹配类型": result["匹配类型"],
            "匹配分数": result.get("匹配分数", 0),
            "匹配状态": result["匹配状态"]
        }
        extended_results.append(extended_result)
    
    df = pd.DataFrame(extended_results)
    
    # 保存到Excel（按工作表分副表）
    output_file = "匹配结果.xlsx"
    print(f"💾 保存结果到 {output_file}（按工作表分副表）...")
    
    with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
        # 主结果表（汇总所有数据）
        df.to_excel(writer, sheet_name='汇总结果', index=False)
        
        # 按工作表分副表
        sheet_names = []
        for sheet_info in excel_names.keys():
            sheet_name = sheet_info.split('(')[0] if '(' in sheet_info else sheet_info
            if sheet_name not in sheet_names:
                sheet_names.append(sheet_name)
        
        for sheet_name in sheet_names:
            # 筛选当前工作表的数据
            sheet_data = [result for result in extended_results if result["工作表"] == sheet_name]
            
            if sheet_data:
                sheet_df = pd.DataFrame(sheet_data)
                # 确保工作表名称符合Excel要求（不超过31字符，不包含特殊字符）
                safe_sheet_name = sheet_name.replace('/', '_').replace('\\', '_').replace('*', '_').replace('[', '_').replace(']', '_').replace(':', '_').replace('?', '_')
                if len(safe_sheet_name) > 31:
                    safe_sheet_name = safe_sheet_name[:28] + "..."
                
                sheet_df.to_excel(writer, sheet_name=safe_sheet_name, index=False)
                print(f"   创建副表: {safe_sheet_name} ({len(sheet_data)} 条记录)")
        
        # 如果有重复匹配，创建重复匹配表
        if duplicates:
            duplicate_data = []
            for path, items in duplicates.items():
                path_parts = split_path_columns(path, max_levels=6)
                for item in items:
                    duplicate_data.append({
                        "重复路径": path,
                        "路径1": path_parts[0],
                        "路径2": path_parts[1], 
                        "路径3": path_parts[2],
                        "路径4": path_parts[3],
                        "路径5": path_parts[4],
                        "路径6": path_parts[5],
                        "匹配类型": item["匹配类型"],
                        "匹配分数": item.get("匹配分数", 0),
                        "工作表": item["工作表"],
                        "名称": item["名称"]
                    })
            
            duplicate_df = pd.DataFrame(duplicate_data)
            duplicate_df.to_excel(writer, sheet_name='重复匹配', index=False)
    
    print(f"✅ 已创建 {len(sheet_names) + 1} 个工作表（汇总结果 + {len(sheet_names)} 个副表）")
    
    # 统计信息
    print("\n📈 生成统计信息...")
    stats = create_statistics(results)
    
    # 输出统计
    print("\n" + "=" * 60)
    print("📊 匹配统计结果")
    print("=" * 60)
    print(f"总计: {stats['总计']}")
    print(f"成功: {stats['成功']} ✅")
    print(f"失败: {stats['失败']} ❌")
    print(f"空名称: {stats['空名称']} ⚪")
    print(f"成功率: {stats['成功率']} 🎯")
    
    if duplicates:
        print(f"重复匹配: {len(duplicates)} 个文件/文件夹 ⚠️")
    
    # 按工作表统计
    sheet_stats = {}
    for result in results:
        sheet = result["工作表"]
        if sheet not in sheet_stats:
            sheet_stats[sheet] = {"成功": 0, "失败": 0, "空名称": 0}
        sheet_stats[sheet][result["匹配状态"]] = sheet_stats[sheet].get(result["匹配状态"], 0) + 1
    
    print("\n📋 各工作表详细统计:")
    print("-" * 60)
    for sheet, stats_detail in sheet_stats.items():
        total = sum(stats_detail.values())
        success_rate = stats_detail.get("成功", 0) / total * 100 if total > 0 else 0
        print(f"{sheet}: 成功{stats_detail.get('成功', 0)} 失败{stats_detail.get('失败', 0)} 空名称{stats_detail.get('空名称', 0)} 成功率{success_rate:.1f}%")
    
    # 保存统计信息到JSON
    print(f"\n💾 保存统计信息到 匹配统计.json...")
    stats_data = {
        "总体统计": stats,
        "分表统计": sheet_stats
    }
    
    if duplicates:
        stats_data["重复匹配统计"] = {
            "重复文件数量": len(duplicates),
            "重复匹配详情": {path: len(items) for path, items in duplicates.items()}
        }
    
    with open("匹配统计.json", 'w', encoding='utf-8') as f:
        json.dump(stats_data, f, ensure_ascii=False, indent=2)
    
    print("\n🎉 匹配任务完成！")
    print("=" * 60)
    
    return results, stats, duplicates

if __name__ == "__main__":
    try:
        import pandas as pd
    except ImportError:
        print("需要安装pandas库: pip install pandas openpyxl")
        exit(1)
    
    results, stats, duplicates = main()