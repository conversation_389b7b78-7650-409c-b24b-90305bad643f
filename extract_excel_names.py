#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import pandas as pd
import json
import os

def extract_excel_first_column_names(excel_file):
    try:
        excel_data = pd.ExcelFile(excel_file)
        result = {}
        
        for sheet_name in excel_data.sheet_names:
            try:
                df = pd.read_excel(excel_file, sheet_name=sheet_name, header=None)
                first_column_data = []
                names_count = 0
                
                for index, row in df.iterrows():
                    if len(row) > 0:
                        cell_value = row[0]
                        
                        if pd.isna(cell_value) or str(cell_value).strip() == '':
                            has_content_after = False
                            for col_idx in range(1, len(row)):
                                if not pd.isna(row[col_idx]) and str(row[col_idx]).strip() != '':
                                    has_content_after = True
                                    break
                            if has_content_after:
                                first_column_data.append("")
                        else:
                            cell_str = str(cell_value).strip()
                            if cell_str != "名称":
                                first_column_data.append(cell_str)
                                names_count += 1
                
                result[f"{sheet_name}({names_count})"] = first_column_data
                
            except Exception:
                result[f"{sheet_name}(0)"] = []
        
        return result
        
    except Exception:
        return {}

def main():
    excel_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), "工业.xlsx")
    
    if not os.path.exists(excel_file):
        return {}
    
    result = extract_excel_first_column_names(excel_file)
    
    output_json = os.path.join(os.path.dirname(excel_file), "excel_names.json")
    with open(output_json, 'w', encoding='utf-8') as f:
        json.dump(result, f, ensure_ascii=False, indent=2)
    
    print(json.dumps(result, ensure_ascii=False, indent=2))
    return result

if __name__ == "__main__":
    try:
        import pandas as pd
    except ImportError:
        print("{}")
        exit(1)
    
    main()